<template>
  <el-dialog
    v-model="visible"
    :title="`${currentStoreInfo?.description || '详情信息'} - 数据详情`"
    width="80%"
    :before-close="handleClose"
  >
    <div class="detail-content" v-loading="detailLoading">
      <div v-if="currentStoreInfo" class="store-info">
        <div class="info-item">
          <span class="label">数据库名称：</span>
          <span class="value">{{ currentStoreInfo.dbName }}</span>
        </div>
        <div class="info-item">
          <span class="label">存储名称：</span>
          <span class="value">{{ currentStoreInfo.storeName }}</span>
        </div>
        <div class="info-item">
          <span class="label">总大小：</span>
          <span class="value">{{ formatBytes(currentStoreInfo.size) }}</span>
        </div>
      </div>

      <div v-if="storeDetailData" class="data-table">
        <div class="table-header">
          <span>数据列表 (共 {{ storeDetailData.total }} 条记录)</span>
        </div>

        <el-table :data="storeDetailData.data" border max-height="400">
          <el-table-column prop="key" label="键名" width="200" />
          <el-table-column prop="size" label="大小" width="100">
            <template #default="scope">
              {{ formatBytes(getObjectSize(scope.row.value)) }}
            </template>
          </el-table-column>
          <el-table-column prop="value" label="值">
            <template #default="scope">
              <div class="value-content">
                <div 
                  class="value-preview clickable"
                  @click="handleValueClick(scope.row, $event)"
                >
                  {{ getValuePreview(scope.row.value) }}
                </div>
                
                <!-- JSON数据展示的Popover -->
                <el-popover
                  ref="jsonPopoverRef"
                  :visible="activePopoverKey === scope.row.key"
                  placement="right"
                  :width="600"
                  trigger="click"
                  transition="none"
                  @hide="handlePopoverHide"
                >
                  <template #reference>
                    <span></span>
                  </template>
                  <div class="json-popover-content" v-loading="popoverLoading">
                    <div class="popover-header">
                      <span class="popover-title">JSON数据详情</span>
                      <el-button
                        link
                        type="primary"
                        size="small"
                        @click="closePopover"
                      >
                        关闭
                      </el-button>
                    </div>
                    <div class="json-editor-container">
                      <SJsonEditor
                        :model-value="formatValueForEditor(activePopoverData)"
                        :read-only="true"
                        :auto-height="true"
                        :max-height="400"
                        :min-height="150"
                      />
                    </div>
                  </div>
                </el-popover>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="scope">
              <el-button 
                link 
                type="danger" 
                size="small"
                @click="handleDeleteItem(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页器 -->
        <div class="pagination-container" v-if="storeDetailData.total > pageSize">
          <el-pagination
            v-model:current-page="currentPage"
            :page-size="pageSize"
            :total="storeDetailData.total"
            layout="prev, pager, next, total"
            @current-change="handlePageChange"
          />
        </div>
      </div>

      <div v-if="!detailLoading && !storeDetailData" class="empty-detail">
        <div class="empty-text">暂无数据</div>
      </div>
    </div>


  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { IndexedDBItem, StoreDetailResponse, StoreDetailItem } from '@src/types/apidoc/cache'
import { formatBytes, getObjectSize } from '@/helper'
import { ElMessageBox } from 'element-plus'
import SJsonEditor from '@/components/common/json-editor/g-json-editor.vue'

/*
|--------------------------------------------------------------------------
| Props 和 Emits 定义
|--------------------------------------------------------------------------
*/
interface Props {
  visible: boolean
  currentStoreInfo: IndexedDBItem | null
  storeDetailData: StoreDetailResponse | null
  detailLoading: boolean
  worker: Worker | null
}

const props = defineProps<Props>()

const emits = defineEmits<{
  'update:visible': [value: boolean]
  'close': []
  'refresh-cache': []
}>()

/*
|--------------------------------------------------------------------------
| 响应式数据
|--------------------------------------------------------------------------
*/
const currentPage = ref(1)
const pageSize = ref(50)

// Popover相关状态
const activePopoverKey = ref<string>('')
const activePopoverData = ref<unknown>(null)
const jsonPopoverRef = ref()
const popoverLoading = ref(false)
const deletingItems = ref<Set<string>>(new Set())

/*
|--------------------------------------------------------------------------
| 计算属性
|--------------------------------------------------------------------------
*/
const visible = computed({
  get: () => props.visible,
  set: (value: boolean) => emits('update:visible', value)
})

/*
|--------------------------------------------------------------------------
| 方法定义
|--------------------------------------------------------------------------
*/
// 获取store详情数据
const getStoreDetail = (dbName: string, storeName: string, pageNum = 1): void => {
  if (!props.worker) return
  props.worker.postMessage({
    type: 'getStoreDetail',
    dbName,
    storeName,
    pageNum,
    pageSize: pageSize.value
  })
}

// 分页切换
const handlePageChange = (page: number): void => {
  if (!props.currentStoreInfo) return
  currentPage.value = page
  getStoreDetail(props.currentStoreInfo.dbName, props.currentStoreInfo.storeName, page)
}

// 关闭详情模态框
const handleClose = (): void => {
  closePopover()
  currentPage.value = 1
  emits('close')
}

// 获取值的预览文本
const getValuePreview = (value: unknown): string => {
  if (value === null || value === undefined) {
    return String(value)
  }
  
  const str = typeof value === 'string' ? value : JSON.stringify(value)
  // 限制预览长度为100个字符
  return str.length > 100 ? str.substring(0, 100) + '...' : str
}

// 格式化值用于编辑器显示
const formatValueForEditor = (value: unknown): string => {
  if (typeof value === 'string') {
    try {
      // 尝试解析为JSON并格式化
      const parsed = JSON.parse(value)
      return JSON.stringify(parsed, null, 2)
    } catch {
      // 如果不是JSON，直接返回字符串
      return value
    }
  }
  return JSON.stringify(value, null, 2)
}

// 处理值点击事件
const handleValueClick = (row: StoreDetailItem, event: Event): void => {
  event.stopPropagation()
  
  // 如果点击的是同一行，则关闭popover
  if (activePopoverKey.value === row.key) {
    closePopover()
    return
  }
  
  // 关闭之前的popover，打开新的
  activePopoverKey.value = row.key
  activePopoverData.value = row.value
}

// 关闭popover
const closePopover = (): void => {
  activePopoverKey.value = ''
  activePopoverData.value = null
}

// 处理popover隐藏事件
const handlePopoverHide = (): void => {
  closePopover()
}

// 删除单个数据项
const handleDeleteItem = async (row: StoreDetailItem): Promise<void> => {
  if (!props.currentStoreInfo || !props.worker) return

  // 如果正在删除，直接返回
  if (deletingItems.value.has(row.key)) return

  try {
    await ElMessageBox.confirm(
      `确定要删除键名为 "${row.key}" 的数据吗？此操作不可撤销。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 添加到删除loading状态
    deletingItems.value.add(row.key)

    // 发送删除单个数据项的消息到worker
    props.worker.postMessage({
      type: 'deleteStoreItem',
      dbName: props.currentStoreInfo.dbName,
      storeName: props.currentStoreInfo.storeName,
      key: row.key
    })

  } catch {
    // 用户取消删除操作，不做任何处理
  }
}

/*
|--------------------------------------------------------------------------
| 监听器
|--------------------------------------------------------------------------
*/
// 监听visible变化，重置状态
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    currentPage.value = 1
    closePopover()
  }
})

// 监听currentStoreInfo变化，获取详情数据
watch(() => props.currentStoreInfo, (newStoreInfo) => {
  if (newStoreInfo && props.visible) {
    getStoreDetail(newStoreInfo.dbName, newStoreInfo.storeName, 1)
  }
})

/*
|--------------------------------------------------------------------------
| 暴露给父组件的方法
|--------------------------------------------------------------------------
*/
defineExpose({
  getStoreDetail,
  handlePageChange,
  handleDeleteItemSuccess: () => {
    // 删除成功后刷新当前页数据
    if (props.currentStoreInfo) {
      getStoreDetail(props.currentStoreInfo.dbName, props.currentStoreInfo.storeName, currentPage.value)
      emits('refresh-cache')
    }
  }
})
</script>
